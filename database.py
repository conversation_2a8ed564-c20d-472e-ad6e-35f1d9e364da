import motor.motor_asyncio
from datetime import datetime
import asyncio
import logging
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('database')

# MongoDB connection pooling and caching
class DatabaseManager:
    """Singleton class to manage MongoDB connections and provide caching"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        # Connection settings with proper pooling
        self.mongo_uri = "mongodb://localhost:27017/"
        self.db_name = "missminutesbot"
        self.client = None
        self.db = None

        # Collection references
        self.collections = {}

        # Cache settings
        self.cache = {}
        self.cache_ttl = {}
        self.default_ttl = 300  # 5 minutes default TTL
        self.max_cache_size = 1000  # Maximum number of items in cache

        # Connection state
        self.is_connected = False
        self.last_connection_attempt = 0
        self.connection_retry_delay = 5  # seconds
        self.connection_lock = asyncio.Lock()

        self._initialized = True

    async def connect(self, force=False):
        """Connect to MongoDB with connection pooling"""
        # Use a lock to prevent multiple connection attempts
        async with self.connection_lock:
            # Check if already connected
            if self.is_connected and not force:
                return True

            # Implement connection throttling
            current_time = time.time()
            if not force and current_time - self.last_connection_attempt < self.connection_retry_delay:
                logger.warning("Connection attempt throttled. Waiting before retry.")
                return False

            self.last_connection_attempt = current_time

            try:
                # Configure connection with proper pooling
                self.client = motor.motor_asyncio.AsyncIOMotorClient(
                    self.mongo_uri,
                    maxPoolSize=50,  # Increase connection pool size
                    minPoolSize=5,   # Maintain minimum connections
                    maxIdleTimeMS=30000,  # Close idle connections after 30 seconds
                    connectTimeoutMS=5000,  # 5 second connection timeout
                    serverSelectionTimeoutMS=5000,  # 5 second server selection timeout
                    retryWrites=True  # Enable retryable writes
                )

                # Test connection
                await self.client.admin.command('ping')

                self.db = self.client[self.db_name]
                self.is_connected = True

                # Initialize collections
                self.collections = {
                    "gangs": self.db["gangs"],
                    "applications": self.db["applications"],
                    "settings": self.db["settings"],
                    "sticky_messages": self.db["sticky_messages"],
                    "tebex_settings": self.db["tebex_settings"],
                    "reaction_roles": self.db["reaction_roles"],
                    "transactions": self.db["transactions"],
                    "guild_settings": self.db["guild_settings"]
                }

                # Create indexes with proper conflict handling
                await self._create_indexes_safely()

                logger.debug("Successfully connected to MongoDB with connection pooling")
                return True

            except Exception as e:
                self.is_connected = False
                logger.error(f"Error connecting to MongoDB: {e}")
                return False

    async def _create_indexes_safely(self):
        """Create indexes safely, handling conflicts and existing indexes"""
        try:
            # Define all indexes with explicit names to avoid conflicts
            index_definitions = [
                {
                    "collection": "gangs",
                    "index": [("guild_id", 1)],
                    "name": "gangs_guild_id_idx",
                    "unique": False
                },
                {
                    "collection": "applications",
                    "index": [("guild_id", 1)],
                    "name": "applications_guild_id_idx",
                    "unique": False
                },
                {
                    "collection": "settings",
                    "index": [("guild_id", 1)],
                    "name": "settings_guild_id_idx",
                    "unique": False
                },
                {
                    "collection": "transactions",
                    "index": [("transaction_id", 1)],
                    "name": "transactions_transaction_id_unique_idx",
                    "unique": True
                },
                {
                    "collection": "guild_settings",
                    "index": [("guild_id", 1)],
                    "name": "guild_settings_guild_id_unique_idx",
                    "unique": True
                }
            ]

            # Create each index safely
            for index_def in index_definitions:
                await self._create_single_index_safely(
                    self.collections[index_def["collection"]],
                    index_def["index"],
                    index_def["name"],
                    index_def["unique"]
                )

            logger.debug("All indexes created successfully")

        except Exception as e:
            logger.error(f"Error creating indexes: {e}")
            # Don't fail the connection for index errors
            pass

    async def _create_single_index_safely(self, collection, index_spec, index_name, unique=False):
        """Create a single index safely, handling existing indexes"""
        try:
            # Check if index already exists
            existing_indexes = await collection.list_indexes().to_list(length=None)
            existing_index_names = [idx.get("name") for idx in existing_indexes]

            if index_name in existing_index_names:
                # Index already exists, check if it matches our requirements
                existing_index = next((idx for idx in existing_indexes if idx.get("name") == index_name), None)
                if existing_index:
                    existing_unique = existing_index.get("unique", False)
                    if existing_unique != unique:
                        # Index exists but with different unique constraint, drop and recreate
                        logger.info(f"Dropping existing index {index_name} due to unique constraint mismatch")
                        await collection.drop_index(index_name)
                        await collection.create_index(index_spec, name=index_name, unique=unique)
                        logger.debug(f"Recreated index {index_name} with unique={unique}")
                    else:
                        logger.debug(f"Index {index_name} already exists with correct settings")
                else:
                    # Create the index
                    await collection.create_index(index_spec, name=index_name, unique=unique)
                    logger.debug(f"Created index {index_name} with unique={unique}")
            else:
                # Index doesn't exist, create it
                await collection.create_index(index_spec, name=index_name, unique=unique)
                logger.debug(f"Created new index {index_name} with unique={unique}")

        except Exception as e:
            # If there's still a conflict, try to resolve it by dropping conflicting indexes
            if "existing index" in str(e).lower() or "index already exists" in str(e).lower():
                try:
                    logger.warning(f"Index conflict detected for {index_name}, attempting to resolve...")

                    # Find and drop any conflicting indexes with auto-generated names
                    for existing_index in existing_indexes:
                        existing_key = existing_index.get("key", {})
                        our_key = dict(index_spec) if isinstance(index_spec, list) else {index_spec: 1}

                        if existing_key == our_key and existing_index.get("name") != index_name:
                            # This is a conflicting index with the same key but different name
                            conflicting_name = existing_index.get("name")
                            logger.info(f"Dropping conflicting index {conflicting_name}")
                            await collection.drop_index(conflicting_name)

                    # Now try to create our index again
                    await collection.create_index(index_spec, name=index_name, unique=unique)
                    logger.info(f"Successfully created index {index_name} after resolving conflicts")

                except Exception as resolve_error:
                    logger.error(f"Failed to resolve index conflict for {index_name}: {resolve_error}")
            else:
                logger.error(f"Error creating index {index_name}: {e}")

    def get_collection(self, name):
        """Get a collection by name, ensuring connection first"""
        if name in self.collections:
            return self.collections[name]
        return None

    async def ensure_connection(self):
        """Ensure database is connected before operations"""
        if not self.is_connected:
            return await self.connect()
        return True

    def cache_get(self, key):
        """Get an item from cache if it exists and is not expired"""
        if key in self.cache and key in self.cache_ttl:
            if self.cache_ttl[key] > time.time():
                return self.cache[key]
            else:
                # Remove expired item
                del self.cache[key]
                del self.cache_ttl[key]
        return None

    def cache_set(self, key, value, ttl=None):
        """Store an item in cache with TTL"""
        # Manage cache size
        if len(self.cache) >= self.max_cache_size:
            # Remove oldest items
            oldest_keys = sorted(self.cache_ttl.items(), key=lambda x: x[1])[:100]
            for old_key, _ in oldest_keys:
                if old_key in self.cache:
                    del self.cache[old_key]
                if old_key in self.cache_ttl:
                    del self.cache_ttl[old_key]

        # Set new cache item
        self.cache[key] = value
        self.cache_ttl[key] = time.time() + (ttl or self.default_ttl)

    def cache_invalidate(self, key_prefix=None):
        """Invalidate cache items by prefix or all if None"""
        if key_prefix is None:
            self.cache.clear()
            self.cache_ttl.clear()
        else:
            keys_to_remove = [k for k in self.cache if k.startswith(key_prefix)]
            for key in keys_to_remove:
                if key in self.cache:
                    del self.cache[key]
                if key in self.cache_ttl:
                    del self.cache_ttl[key]

# Create global database manager instance
db_manager = DatabaseManager()

# Initialize collections for backward compatibility
MONGO_CLIENT = None
DB = None
GANGS_COLLECTION = None
APPLICATIONS_COLLECTION = None
SETTINGS_COLLECTION = None
STICKY_MESSAGES_COLLECTION = None
TEBEX_SETTINGS_COLLECTION = None
REACTION_ROLES_COLLECTION = None
TRANSACTIONS_COLLECTION = None
GUILD_SETTINGS_COLLECTION = None

async def init_db():
    """Initialize database connection and setup with fresh database support"""
    global MONGO_CLIENT, DB, GANGS_COLLECTION, APPLICATIONS_COLLECTION, SETTINGS_COLLECTION
    global STICKY_MESSAGES_COLLECTION, TEBEX_SETTINGS_COLLECTION, REACTION_ROLES_COLLECTION
    global TRANSACTIONS_COLLECTION, GUILD_SETTINGS_COLLECTION

    # Connect using the manager with fresh database handling
    success = await db_manager.connect()
    if not success:
        logger.error("Failed to connect to MongoDB")
        return False

    # Set global variables for backward compatibility
    MONGO_CLIENT = db_manager.client
    DB = db_manager.db
    GANGS_COLLECTION = db_manager.get_collection("gangs")
    APPLICATIONS_COLLECTION = db_manager.get_collection("applications")
    SETTINGS_COLLECTION = db_manager.get_collection("settings")
    STICKY_MESSAGES_COLLECTION = db_manager.get_collection("sticky_messages")
    TEBEX_SETTINGS_COLLECTION = db_manager.get_collection("tebex_settings")
    REACTION_ROLES_COLLECTION = db_manager.get_collection("reaction_roles")
    TRANSACTIONS_COLLECTION = db_manager.get_collection("transactions")
    GUILD_SETTINGS_COLLECTION = db_manager.get_collection("guild_settings")

    # Initialize default documents if they don't exist
    try:
        collections_init = {
            GANGS_COLLECTION: {"_id": "gangs", "roles": {}, "members": {}, "leaders": {}, "strikes": {}},
            APPLICATIONS_COLLECTION: {"_id": "applications", "forms": {}, "channels": {}, "status": {}},
            SETTINGS_COLLECTION: {"_id": "settings", "welcome": {}, "vanity": {}, "notifications": {}, "join_role_id": None},
            STICKY_MESSAGES_COLLECTION: {"_id": "sticky_messages", "messages": {}},
            TEBEX_SETTINGS_COLLECTION: {"_id": "tebex_settings", "channel_id": None, "webhook_url": None},
            REACTION_ROLES_COLLECTION: {"_id": "reaction_roles", "roles": {}, "message_id": None, "channel_id": None}
        }

        for collection, default_doc in collections_init.items():
            existing_doc = await collection.find_one({"_id": default_doc["_id"]})
            if not existing_doc:
                await collection.insert_one(default_doc)

        logger.debug("Successfully connected to MongoDB and initialized collections")
    except Exception as e:
        logger.error(f"Error initializing collections: {e}")
        return False

    return True

async def save_guild_settings(guild_id, settings):
    """Save guild settings with caching"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return False

        collection = db_manager.get_collection("guild_settings")
        if collection is None:
            logger.error("Guild settings collection not available")
            return False

        # Update the database
        await collection.update_one(
            {"guild_id": guild_id},
            {"$set": settings},
            upsert=True
        )

        # Invalidate cache for this guild
        db_manager.cache_invalidate(f"guild_settings:{guild_id}")

        return True
    except Exception as e:
        logger.error(f"Error saving guild settings: {e}")
        return False

async def get_guild_settings(guild_id):
    """Get guild settings with caching"""
    try:
        # Check cache first
        cache_key = f"guild_settings:{guild_id}"
        cached_data = db_manager.cache_get(cache_key)
        if cached_data is not None:
            return cached_data

        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return {}

        collection = db_manager.get_collection("guild_settings")
        if collection is None:
            logger.error("Guild settings collection not available")
            return {}

        # Get from database
        settings = await collection.find_one({"guild_id": guild_id}, {"_id": 0})
        result = settings if settings else {}

        # Cache the result
        db_manager.cache_set(cache_key, result)

        return result
    except Exception as e:
        logger.error(f"Error loading guild settings: {e}")
        return {}

async def save_data(data):
    """Save bot data with optimized connection handling and caching"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return False

        # Process data in batches for better performance
        try:
            # Process gang roles data to ensure consistent string IDs
            processed_roles = {}
            for gang_name, gang_data in data["gangs"].get("roles", {}).items():
                processed_gang_data = gang_data.copy()
                processed_gang_data["leader"] = str(gang_data["leader"])
                processed_gang_data["leader_role"] = str(gang_data["leader_role"])
                processed_gang_data["members"] = [str(m) if isinstance(m, (int, str)) else m for m in gang_data["members"]]
                processed_roles[gang_name] = processed_gang_data

            gangs_data = {
                "_id": "gangs",
                "roles": processed_roles,
                "leaders": {str(k): str(v) for k, v in data["gangs"].get("leaders", {}).items()},
                "strikes": data["gangs"].get("strikes", {}),
                "invitations": data["gangs"].get("invitations", {})  # Save gang invitations for persistence
            }

            # Create a list of operations to execute in parallel
            operations = []

            # Add each collection update as a separate task
            operations.append(db_manager.get_collection("gangs").replace_one(
                {"_id": "gangs"}, gangs_data, upsert=True
            ))

            # Process applications data to ensure all keys are strings
            applications_data = data["applications"].copy()
            if "status" in applications_data:
                # Convert all user IDs to strings in the status dictionary
                applications_data["status"] = {
                    str(user_id): status_data
                    for user_id, status_data in applications_data["status"].items()
                }

            operations.append(db_manager.get_collection("applications").replace_one(
                {"_id": "applications"},
                {"_id": "applications", **applications_data},
                upsert=True
            ))

            operations.append(db_manager.get_collection("settings").replace_one(
                {"_id": "settings"},
                {"_id": "settings", **data["settings"]},
                upsert=True
            ))

            operations.append(db_manager.get_collection("sticky_messages").replace_one(
                {"_id": "sticky_messages"},
                {"_id": "sticky_messages", "messages": {str(k): v for k, v in data["sticky_messages"].items()}},
                upsert=True
            ))

            operations.append(db_manager.get_collection("tebex_settings").replace_one(
                {"_id": "tebex_settings"},
                {"_id": "tebex_settings", **data["tebex_settings"]},
                upsert=True
            ))

            # Process reaction_roles data to ensure consistent structure
            reaction_roles_data = {"_id": "reaction_roles"}

            # Extract the roles and config from the data
            if "reaction_roles" in data:
                # Add message_id and channel_id
                if "message_id" in data["reaction_roles"]:
                    reaction_roles_data["message_id"] = data["reaction_roles"]["message_id"]
                if "channel_id" in data["reaction_roles"]:
                    reaction_roles_data["channel_id"] = data["reaction_roles"]["channel_id"]

                # Process roles - ensure they're in a flat structure
                roles = {}
                if "roles" in data["reaction_roles"]:
                    if isinstance(data["reaction_roles"]["roles"], dict):
                        # Check if it's already a flat emoji->role_id mapping
                        has_flat_structure = all(
                            isinstance(k, str) and isinstance(v, (int, str)) and k not in ('config', 'roles')
                            for k, v in data["reaction_roles"]["roles"].items()
                        )

                        if has_flat_structure:
                            roles = data["reaction_roles"]["roles"]
                        # If it's nested, try to extract the emoji->role_id mapping
                        elif "roles" in data["reaction_roles"]["roles"] and isinstance(data["reaction_roles"]["roles"]["roles"], dict):
                            roles = data["reaction_roles"]["roles"]["roles"]

                reaction_roles_data["roles"] = roles

                # Process config
                config = {"allow_multiple": False}  # Default config
                if "config" in data["reaction_roles"]:
                    config = data["reaction_roles"]["config"]
                elif "roles" in data["reaction_roles"] and "config" in data["reaction_roles"]["roles"]:
                    config = data["reaction_roles"]["roles"]["config"]

                reaction_roles_data["config"] = config

            operations.append(db_manager.get_collection("reaction_roles").replace_one(
                {"_id": "reaction_roles"},
                reaction_roles_data,
                upsert=True
            ))

            # Execute all operations in parallel
            await asyncio.gather(*operations)

            # Invalidate all related caches
            db_manager.cache_invalidate("bot_data")

            logger.debug("Data saved successfully to MongoDB")
            return True

        except Exception as e:
            logger.error(f"Error processing data: {e}")
            return False

    except Exception as e:
        logger.error(f"Error saving data to MongoDB: {e}")
        return False

async def load_data():
    """Load bot data with caching for better performance"""
    try:
        # Check cache first
        cache_key = "bot_data:all"
        cached_data = db_manager.cache_get(cache_key)
        if cached_data is not None:
            logger.debug("Loaded data from cache")
            return cached_data

        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return None

        # Create tasks for parallel execution
        tasks = [
            db_manager.get_collection("gangs").find_one({"_id": "gangs"}, {"_id": 0}),
            db_manager.get_collection("applications").find_one({"_id": "applications"}, {"_id": 0}),
            db_manager.get_collection("settings").find_one({"_id": "settings"}, {"_id": 0}),
            db_manager.get_collection("sticky_messages").find_one({"_id": "sticky_messages"}, {"_id": 0}),
            db_manager.get_collection("tebex_settings").find_one({"_id": "tebex_settings"}, {"_id": 0}),
            db_manager.get_collection("reaction_roles").find_one({"_id": "reaction_roles"}, {"_id": 0})
        ]

        # Execute all queries in parallel
        results = await asyncio.gather(*tasks)

        # Process results
        data = {
            "gangs": results[0] or {},
            "applications": results[1] or {},
            "settings": results[2] or {},
            "sticky_messages": (results[3] or {}).get("messages", {}),
            "tebex_settings": results[4] or {},
            "reaction_roles": results[5] or {}
        }

        # Cache the result
        db_manager.cache_set(cache_key, data, ttl=300)  # Cache for 5 minutes

        logger.debug("Data loaded successfully from MongoDB")
        return data
    except Exception as e:
        logger.error(f"Error loading data from MongoDB: {e}")
        return None

async def save_transaction(transaction_data):
    """Save transaction data with optimized connection handling"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return False

        collection = db_manager.get_collection("transactions")
        if collection is None:
            logger.error("Transactions collection not available")
            return False

        # Add timestamp if not present
        if "timestamp" not in transaction_data:
            transaction_data["timestamp"] = datetime.now()

        # Check if this is an update (transaction with this ID already exists)
        transaction_id = transaction_data.get('transaction_id')
        is_update = False

        if transaction_id:
            # Check if transaction already exists
            existing = await collection.find_one({"transaction_id": transaction_id})
            is_update = existing is not None

        # Insert or update with retry logic
        for attempt in range(3):  # Try up to 3 times
            try:
                if is_update:
                    # Update existing transaction
                    await collection.replace_one(
                        {"transaction_id": transaction_id},
                        transaction_data
                    )
                else:
                    # Insert new transaction
                    await collection.insert_one(transaction_data)

                # Invalidate transactions cache
                db_manager.cache_invalidate("transactions")

                return True
            except Exception as e:
                if attempt < 2:  # Don't sleep on the last attempt
                    await asyncio.sleep(0.5 * (attempt + 1))  # Exponential backoff
                else:
                    logger.error(f"Failed to save transaction after retries: {e}")
                    return False

    except Exception as e:
        logger.error(f"Error saving transaction to MongoDB: {e}")
        return False

async def get_transactions(limit=100, skip=0, sort_by="timestamp", sort_dir=-1):
    """Get transactions with pagination, sorting and caching"""
    try:
        # Check cache for common queries
        if limit == 100 and skip == 0 and sort_by == "timestamp" and sort_dir == -1:
            cache_key = "transactions:recent"
            cached_data = db_manager.cache_get(cache_key)
            if cached_data is not None:
                return cached_data

        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return []

        collection = db_manager.get_collection("transactions")
        if collection is None:
            logger.error("Transactions collection not available")
            return []

        # Query with pagination and sorting
        cursor = collection.find({}, {"_id": 0})

        # Apply sorting
        cursor = cursor.sort(sort_by, sort_dir)

        # Apply pagination
        if skip > 0:
            cursor = cursor.skip(skip)
        if limit > 0:
            cursor = cursor.limit(limit)

        # Execute query
        transactions = await cursor.to_list(length=limit)

        # Cache only for standard queries
        if limit == 100 and skip == 0 and sort_by == "timestamp" and sort_dir == -1:
            db_manager.cache_set("transactions:recent", transactions, ttl=60)  # Cache for 1 minute

        return transactions
    except Exception as e:
        logger.error(f"Error getting transactions from MongoDB: {e}")
        return []


async def init_fresh_database():
    """Initialize a completely fresh database from scratch"""
    try:
        logger.info("Initializing fresh database...")

        # Connect to MongoDB with force refresh
        success = await db_manager.connect(force=True)
        if not success:
            logger.error("Failed to connect to MongoDB for fresh initialization")
            return False

        # Reinitialize collections (in case they were corrupted)
        db_manager.collections = {
            "gangs": db_manager.db["gangs"],
            "applications": db_manager.db["applications"],
            "settings": db_manager.db["settings"],
            "sticky_messages": db_manager.db["sticky_messages"],
            "tebex_settings": db_manager.db["tebex_settings"],
            "reaction_roles": db_manager.db["reaction_roles"],
            "transactions": db_manager.db["transactions"],
            "guild_settings": db_manager.db["guild_settings"],
            "persistent_views": db_manager.db["persistent_views"]
        }

        # Create indexes safely (this will handle conflicts)
        await db_manager._create_indexes_safely()

        # Initialize default documents if they don't exist
        collections_init = {
            db_manager.collections["gangs"]: {"_id": "gangs", "roles": {}, "members": {}, "leaders": {}, "strikes": {}},
            db_manager.collections["applications"]: {"_id": "applications", "forms": {}, "channels": {}, "status": {}},
            db_manager.collections["settings"]: {"_id": "settings", "welcome": {}, "vanity": {}, "notifications": {}, "join_role_id": None},
            db_manager.collections["sticky_messages"]: {"_id": "sticky_messages", "messages": {}},
            db_manager.collections["tebex_settings"]: {"_id": "tebex_settings", "channel_id": None, "webhook_url": None},
            db_manager.collections["reaction_roles"]: {"_id": "reaction_roles", "roles": {}, "message_id": None, "channel_id": None}
        }

        for collection, default_doc in collections_init.items():
            try:
                existing_doc = await collection.find_one({"_id": default_doc["_id"]})
                if not existing_doc:
                    await collection.insert_one(default_doc)
                    logger.debug(f"Initialized default document for {collection.name}")
                else:
                    logger.debug(f"Default document already exists for {collection.name}")
            except Exception as e:
                logger.warning(f"Error initializing default document for {collection.name}: {e}")

        logger.info("Fresh database initialization completed successfully")
        return True

    except Exception as e:
        logger.error(f"Error during fresh database initialization: {e}")
        return False


async def drop_and_recreate_database():
    """Drop the entire database and recreate it from scratch (use with caution!)"""
    try:
        logger.warning("DROPPING ENTIRE DATABASE - This will delete all data!")

        # Connect to MongoDB
        success = await db_manager.connect(force=True)
        if not success:
            logger.error("Failed to connect to MongoDB for database recreation")
            return False

        # Drop the entire database
        await db_manager.client.drop_database(db_manager.db_name)
        logger.info(f"Dropped database {db_manager.db_name}")

        # Recreate the database
        db_manager.db = db_manager.client[db_manager.db_name]

        # Initialize fresh database
        return await init_fresh_database()

    except Exception as e:
        logger.error(f"Error dropping and recreating database: {e}")
        return False


async def save_persistent_view(view_id, transaction_id, view_type, show_claim_button, message_id=None, channel_id=None):
    """Save persistent view data to MongoDB"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return False

        collection = db_manager.get_collection("persistent_views")
        if collection is None:
            logger.error("Persistent views collection not available")
            return False

        view_data = {
            '_id': view_id,
            'view_id': view_id,
            'transaction_id': transaction_id,
            'view_type': view_type,
            'show_claim_button': show_claim_button,
            'message_id': message_id,
            'channel_id': channel_id,
            'created_at': datetime.now(),
            'active': True
        }

        # Insert or update the view
        result = await collection.replace_one(
            {'_id': view_id},
            view_data,
            upsert=True
        )

        logger.info(f"Persistent view {view_id} saved for transaction {transaction_id}")
        return True

    except Exception as e:
        logger.error(f"Error saving persistent view to database: {e}")
        return False


async def get_persistent_view(view_id):
    """Retrieve persistent view data from MongoDB"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return None

        collection = db_manager.get_collection("persistent_views")
        if collection is None:
            logger.error("Persistent views collection not available")
            return None

        view_data = await collection.find_one({'view_id': view_id, 'active': True})
        return view_data
    except Exception as e:
        logger.error(f"Error retrieving persistent view {view_id}: {e}")
        return None


async def deactivate_persistent_view(view_id):
    """Mark a persistent view as inactive"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return False

        collection = db_manager.get_collection("persistent_views")
        if collection is None:
            logger.error("Persistent views collection not available")
            return False

        result = await collection.update_one(
            {'view_id': view_id},
            {'$set': {'active': False, 'deactivated_at': datetime.now()}}
        )
        logger.info(f"Persistent view {view_id} deactivated")
        return True
    except Exception as e:
        logger.error(f"Error deactivating persistent view {view_id}: {e}")
        return False


async def get_all_active_persistent_views():
    """Get all active persistent views for restoration on bot startup"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return []

        collection = db_manager.get_collection("persistent_views")
        if collection is None:
            logger.error("Persistent views collection not available")
            return []

        cursor = collection.find({'active': True})
        views = await cursor.to_list(length=None)
        return views
    except Exception as e:
        logger.error(f"Error retrieving active persistent views: {e}")
        return []

async def save_persistent_view(view_id, transaction_id, view_type, show_claim_button, message_id=None, channel_id=None):
    """Save persistent view data to MongoDB"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return False

        collection = db_manager.get_collection("persistent_views")
        if collection is None:
            logger.error("Persistent views collection not available")
            return False

        view_data = {
            '_id': view_id,
            'view_id': view_id,
            'transaction_id': transaction_id,
            'view_type': view_type,
            'show_claim_button': show_claim_button,
            'message_id': message_id,
            'channel_id': channel_id,
            'created_at': datetime.now(),
            'active': True
        }

        # Insert or update the view
        result = await collection.replace_one(
            {'_id': view_id},
            view_data,
            upsert=True
        )

        logger.info(f"Persistent view {view_id} saved for transaction {transaction_id}")
        return True

    except Exception as e:
        logger.error(f"Error saving persistent view to database: {e}")
        return False

async def get_persistent_view(view_id):
    """Retrieve persistent view data from MongoDB"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return None

        collection = db_manager.get_collection("persistent_views")
        if collection is None:
            logger.error("Persistent views collection not available")
            return None

        view_data = await collection.find_one({'view_id': view_id, 'active': True})
        return view_data
    except Exception as e:
        logger.error(f"Error retrieving persistent view {view_id}: {e}")
        return None

async def deactivate_persistent_view(view_id):
    """Mark a persistent view as inactive"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return False

        collection = db_manager.get_collection("persistent_views")
        if collection is None:
            logger.error("Persistent views collection not available")
            return False

        result = await collection.update_one(
            {'view_id': view_id},
            {'$set': {'active': False, 'deactivated_at': datetime.now()}}
        )
        logger.info(f"Persistent view {view_id} deactivated")
        return True
    except Exception as e:
        logger.error(f"Error deactivating persistent view {view_id}: {e}")
        return False

async def get_all_active_persistent_views():
    """Get all active persistent views for restoration on bot startup"""
    try:
        # Ensure database connection
        if not await db_manager.ensure_connection():
            logger.error("Failed to connect to database")
            return []

        collection = db_manager.get_collection("persistent_views")
        if collection is None:
            logger.error("Persistent views collection not available")
            return []

        cursor = collection.find({'active': True})
        views = await cursor.to_list(length=None)
        return views
    except Exception as e:
        logger.error(f"Error retrieving active persistent views: {e}")
        return []