import requests
import json
import uuid
import time
from datetime import datetime

def generate_unique_transaction_id():
    """
    Generate a unique transaction ID for testing purposes.
    Format: TXN_{timestamp}_{short_uuid}
    Example: TXN_20250803_A1B2C3D4

    This ensures:
    - Uniqueness across multiple test runs
    - Realistic format similar to actual transaction IDs
    - Easy identification of test transactions by date
    - No conflicts with existing database records
    """
    # Get current timestamp in YYYYMMDD format
    timestamp = datetime.now().strftime("%Y%m%d")

    # Generate a short UUID (first 8 characters for readability)
    short_uuid = str(uuid.uuid4()).replace('-', '').upper()[:8]

    # Combine into a realistic transaction ID format
    transaction_id = f"TXN_{timestamp}_{short_uuid}"

    return transaction_id

def generate_alternative_transaction_id():
    """
    Alternative method using timestamp + incremental approach.
    Format: TEST_{unix_timestamp}_{random_suffix}
    Example: TEST_1704326400_R7K9
    """
    # Unix timestamp for uniqueness
    unix_timestamp = int(time.time())

    # Random 4-character suffix
    random_suffix = str(uuid.uuid4()).replace('-', '').upper()[:4]

    return f"TEST_{unix_timestamp}_{random_suffix}"

def test_webhook(webhook_url):
    # Generate unique transaction ID for this test run
    unique_transaction_id = generate_unique_transaction_id()

    # Test data matching the expected format with unique transaction ID
    data = {
        "content": f"Crimson has received a payment ╽ From: TestUser1aa23233 ╽ Price: $25.aa002 ╽ Package: VIP Pacaka2age ╽ Transaction ID: {unique_transaction_id} ╽ Email: <EMAIL>"
    }

    print(f"🔄 Testing webhook with unique Transaction ID: {unique_transaction_id}")

    try:
        # Send test webhook
        response = requests.post(webhook_url, json=data)
        
        # Discord webhook successful response is 204
        if response.status_code == 204:
            print("✓ Webhook test successful - Message sent to Discord")
        else:
            print(f"⚠ Webhook Response Status: {response.status_code}")
            if response.text:
                print(f"Response Details: {response.text}")
            else:
                print("No additional response details available")
                
    except requests.exceptions.RequestException as e:
        print(f"⚠ Connection Error: {str(e)}")
    except Exception as e:
        print(f"⚠ Unexpected Error: {str(e)}")

if __name__ == "__main__":
    # Replace with your webhook URL
    webhook_url = "https://discord.com/api/webhooks/1378086188262490142/dpQesZefRzldGSouDg0ieytvKq08eaEpXrCkpD22jXi9wbI_zbmIWepRuXrR2YxfbogB"
    test_webhook(webhook_url)