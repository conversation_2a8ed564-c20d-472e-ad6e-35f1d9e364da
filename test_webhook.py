import requests
import json
import time
import random
import string

def generate_unique_transaction_id():
    """Generate a unique transaction ID using timestamp and random characters"""
    # Get current timestamp
    timestamp = str(int(time.time()))

    # Generate random alphanumeric string
    random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))

    # Combine timestamp and random chars for uniqueness
    transaction_id = f"TEST_{timestamp}_{random_chars}"

    return transaction_id

def test_webhook(webhook_url):
    # Generate unique transaction ID
    unique_transaction_id = generate_unique_transaction_id()

    # Test data matching the expected format with unique transaction ID
    data = {
        "content": f"Crimson has received a payment ╽ From: TestUser1aa23233 ╽ Price: $25.aa002 ╽ Package: VIP Pacaka2age ╽ Transaction ID: {unique_transaction_id} ╽ Email: <EMAIL>"
    }

    print(f"Generated unique transaction ID: {unique_transaction_id}")

    try:
        # Send test webhook
        response = requests.post(webhook_url, json=data)
        
        # Discord webhook successful response is 204
        if response.status_code == 204:
            print("✓ Webhook test successful - Message sent to Discord")
        else:
            print(f"⚠ Webhook Response Status: {response.status_code}")
            if response.text:
                print(f"Response Details: {response.text}")
            else:
                print("No additional response details available")
                
    except requests.exceptions.RequestException as e:
        print(f"⚠ Connection Error: {str(e)}")
    except Exception as e:
        print(f"⚠ Unexpected Error: {str(e)}")

if __name__ == "__main__":
    # Replace with your webhook URL
    webhook_url = "https://discord.com/api/webhooks/1378086188262490142/dpQesZefRzldGSouDg0ieytvKq08eaEpXrCkpD22jXi9wbI_zbmIWepRuXrR2YxfbogB"
    test_webhook(webhook_url)