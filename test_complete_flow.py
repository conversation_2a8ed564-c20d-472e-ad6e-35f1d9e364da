#!/usr/bin/env python3
"""
Complete test of the webhook processing flow including on_message event handling.
This test verifies that the entire flow from webhook to Discord notification works correctly.
"""

import asyncio
import sys
import os
from datetime import datetime, timezone

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the bot module
import bot
from bot import on_message, process_payment_notification

class MockMessage:
    """Mock Discord message object for testing"""
    def __init__(self, content, channel_id, webhook_id=None):
        self.content = content
        self.channel = MockChannel(channel_id)
        self.guild = MockGuild()
        self.webhook_id = webhook_id  # Set to simulate webhook message
        self.author = MockUser(webhook_id or 123456789, "Tebex Webhook")
    
    async def delete(self):
        """Mock delete method"""
        print("✓ Webhook message deleted")

class MockUser:
    """Mock Discord user object"""
    def __init__(self, user_id, name):
        self.id = user_id
        self.name = name
        self.bot = False  # Add bot attribute

class MockChannel:
    """Mock Discord channel object"""
    def __init__(self, channel_id):
        self.id = channel_id
        self.name = "tebex-notifications"
        self.history_messages = []
    
    async def send(self, embed=None, view=None):
        """Mock send method"""
        print(f"✅ INTERACTIVE MESSAGE SENT TO DISCORD!")
        print(f"   📍 Channel: {self.name} (ID: {self.id})")
        if embed:
            print(f"   🎨 Embed Title: {embed.title}")
            print(f"   🌈 Embed Color: {hex(embed.color.value) if embed.color else 'None'}")
            print(f"   📝 Description: {embed.description[:100]}...")
        if view:
            print(f"   🔘 Interactive View: {type(view).__name__}")
            print(f"   🔲 Button Count: {len(view.children)}")
            for i, child in enumerate(view.children):
                if hasattr(child, 'label'):
                    print(f"      Button {i+1}: '{child.label}' (ID: {child.custom_id})")
                    print(f"                 Style: {child.style}, Emoji: {child.emoji}")
        return MockSentMessage()
    
    def history(self, limit=None):
        """Mock history method"""
        return MockAsyncIterator(self.history_messages)

class MockAsyncIterator:
    """Mock async iterator for channel history"""
    def __init__(self, items):
        self.items = items
        self.index = 0
    
    def __aiter__(self):
        return self
    
    async def __anext__(self):
        if self.index >= len(self.items):
            raise StopAsyncIteration
        item = self.items[self.index]
        self.index += 1
        return item

class MockSentMessage:
    """Mock sent message object"""
    def __init__(self):
        self.id = 987654321  # Mock message ID

class MockGuild:
    """Mock Discord guild object"""
    def __init__(self):
        self.icon = None

async def test_complete_webhook_flow():
    """Test the complete webhook processing flow including on_message event"""
    print("🔄 Testing complete webhook flow...")
    
    # Set up mock environment
    bot.tebex_channel = 123456789  # Mock channel ID
    
    # Test webhook content (same format as test_webhook.py)
    test_content = "Crimson has received a payment ╽ From: TestUser123 ╽ Price: $25.00 ╽ Package: VIP Package ╽ Transaction ID: TXN_20250804_COMPLETE_TEST ╽ Email: <EMAIL>"
    
    # Create mock webhook message
    mock_message = MockMessage(test_content, bot.tebex_channel, webhook_id=1378086188262490142)
    
    print(f"📨 Simulating webhook message:")
    print(f"   Content: {test_content[:80]}...")
    print(f"   Webhook ID: {mock_message.webhook_id}")
    print(f"   Channel ID: {mock_message.channel.id}")
    
    try:
        # Test the complete on_message flow (this should detect webhook and call process_payment_notification)
        await on_message(mock_message)
        print("✅ Complete webhook flow test completed successfully!")
        
    except Exception as e:
        print(f"❌ Complete webhook flow test failed: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function"""
    print("🚀 Starting complete webhook flow test...")
    
    # Initialize database connection
    try:
        from database import init_db
        await init_db()
        print("✓ Database connection initialized")
    except Exception as e:
        print(f"⚠ Database initialization failed: {e}")
    
    # Note: We can't mock bot.user, but the test should still work
    
    # Run the complete webhook flow test
    await test_complete_webhook_flow()
    
    print("🏁 Complete test finished!")
    print()
    print("📋 SUMMARY:")
    print("   If you see '✅ INTERACTIVE MESSAGE SENT TO DISCORD!' above,")
    print("   then the persistent interactive view system is working correctly!")
    print("   The Discord notification should show:")
    print("   - Green embed (color: 0x00FF00)")
    print("   - 'Transaction Received' title")
    print("   - Two interactive buttons: 'View Details' and 'Mark as Claimed'")
    print("   - Buttons that remain functional indefinitely")

if __name__ == "__main__":
    # Run the test
    asyncio.run(main())
